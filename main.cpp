#include <iostream>
#include <vector>
#include <thread>
#include <functional>
#include "BackgroundTask.h"

using namespace std;

vector<int> &f(vector<int> &vec)
{
	BackgroundTask task(vec);
	vector<int> a{2, 4, 6, 8, 10};
	// thread(函数,这里传递)的参数是拷贝方式，即使函数定义的是引用
	// 所以要ref包裹才能传递引用。
	thread t(task, ref(a));
	// 线程守护，保证主函数推出前，等待线程返回。
	ThreadGuard g(t);
	return vec;
}

void process_big_object(unique_ptr<vector<int>> p)
{

}

int main()
{
	vector<int> vec = {1, 2, 4, 5, 6};
	f(vec);
	for (int i = 0; i < vec.size(); i++)
	{
		cout << vec[i] << " ";
	}
	cout << endl;
	for (auto b : vec)
	{
		cout << b << " ";
	}
	BackgroundTask task(vec);
	unique_ptr<BackgroundTask> p(task);
}